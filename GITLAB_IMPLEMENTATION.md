# GitLab Provider Implementation

This document describes the implementation of the missing GitLab functionality in the `GitLabProvider` class.

## Overview

The GitLab provider now supports the following previously unimplemented methods:
- `getNotifications()` - Fetches GitLab todos as notifications
- `markNotificationAsRead()` - Marks GitLab todos as done
- `getRepoEvents()` - Fetches project events from GitLab

## Implementation Details

### 1. Data Models

Added GitLab-specific data models with proper JSON annotations for Moshi parsing:

#### Notification Models (GitLab Todos)
- `GitLabTodo` - Main todo object
- `GitLabTodoProject` - Nested project information
- `GitLabTodoAuthor` - Nested author information  
- `GitLabTodoTarget` - Nested target information (Issue, MergeRequest, etc.)

#### Event Models
- `GitLabEvent` - Main event object
- `GitLabEventAuthor` - Nested author information

### 2. API Endpoints Used

#### Notifications (Todos)
- **GET** `/api/v4/todos?state=pending` - Fetches pending todos
- **POST** `/api/v4/todos/:id/mark_as_done` - Marks a todo as done

#### Repository Events
- **GET** `/api/v4/projects/:id/events` - Fetches project events
  - Uses URL-encoded project path (e.g., "org/repo" → "org%2Frepo")

### 3. Mapping to Unified Models

#### GitLab Todo → Notification
- `id` → `id` (converted to string)
- `target.title` or `body` → `title`
- `targetType` → `type`
- `project.pathWithNamespace` → `repositoryName`
- `actionName` → `reason`
- `updatedAt` → `updatedAt`
- `targetUrl` → `url`
- `state == "pending"` → `unread`

#### GitLab Event → RepoEvent
- `id` → `id` (converted to string)
- `actionName` → `type`
- `author.username` → `actorName`
- `author.avatarUrl` → `actorAvatarUrl`
- `createdAt` → `createdAt`
- `true` → `isPublic` (events are visible if user has project access)

### 4. Error Handling

- **Notifications & Events**: Return empty lists on errors to maintain compatibility
- **Mark as Read**: Re-throws exceptions to let the repository layer handle them
- All methods use proper try-catch blocks and follow existing patterns

### 5. Key Features

- **URL Encoding**: Project paths are properly URL-encoded for GitLab API
- **Null Safety**: All nullable fields are handled gracefully
- **Caching Integration**: Works with existing `GitRepository` caching system
- **Consistent Patterns**: Follows same structure as existing GitLab methods

## Testing

Added basic unit tests in `GitLabProviderTest.kt`:
- Provider configuration validation
- Error handling with invalid tokens
- URL encoding for special characters
- Basic structure verification

## Usage

The GitLab provider now works seamlessly with the existing multi-provider system:

```kotlin
// Get notifications (todos)
val notifications = gitRepository.getNotifications(token)

// Mark notification as read
gitRepository.markNotificationAsRead(token, notificationId)

// Get repository events
val events = gitRepository.getRepoEvents(token, "org/repo-name")
```

## Notes

- GitLab's "todos" concept maps well to GitHub's "notifications"
- GitLab events are project-specific and require project access
- All implementations maintain backward compatibility
- Error handling ensures the app doesn't crash on API failures

## Future Enhancements

Potential improvements could include:
- More sophisticated error handling with specific error types
- Pagination support for large result sets
- Additional filtering options for todos and events
- Caching for repository events
