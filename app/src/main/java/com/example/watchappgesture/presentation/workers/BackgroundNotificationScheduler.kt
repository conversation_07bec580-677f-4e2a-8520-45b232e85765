package com.example.watchappgesture.presentation.workers

import android.content.Context
import android.util.Log
import androidx.work.*
import com.example.watchappgesture.presentation.settings.BackgroundNotificationSettings
import kotlinx.coroutines.flow.first
import java.util.concurrent.TimeUnit

/**
 * Manages scheduling of background notification checks using WorkManager
 */
object BackgroundNotificationScheduler {
    
    private const val TAG = "BackgroundNotificationScheduler"
    
    /**
     * Schedules or updates background notification checks based on user settings
     */
    suspend fun scheduleNotificationChecks(context: Context) {
        val settings = BackgroundNotificationSettings.getSettings(context).first()
        
        if (settings.enabled) {
            Log.d(TAG, "Scheduling background notifications every ${settings.frequency.minutes} minutes")
            schedulePeriodicWork(context, settings.frequency)
        } else {
            Log.d(TAG, "Background notifications disabled, cancelling scheduled work")
            cancelNotificationChecks(context)
        }
    }
    
    /**
     * Schedules periodic work for notification checks
     */
    private fun schedulePeriodicWork(
        context: Context,
        frequency: BackgroundNotificationSettings.CheckFrequency
    ) {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .setRequiresBatteryNotLow(true)
            .build()
        
        val workRequest = PeriodicWorkRequestBuilder<GitNotificationWorker>(
            frequency.minutes.toLong(),
            TimeUnit.MINUTES
        )
            .setConstraints(constraints)
            .setBackoffCriteria(
                BackoffPolicy.LINEAR,
                PeriodicWorkRequest.MIN_BACKOFF_MILLIS,
                TimeUnit.MILLISECONDS
            )
            .addTag(GitNotificationWorker.TAG)
            .build()
        
        WorkManager.getInstance(context).enqueueUniquePeriodicWork(
            GitNotificationWorker.WORK_NAME,
            ExistingPeriodicWorkPolicy.UPDATE,
            workRequest
        )
        
        Log.d(TAG, "Scheduled periodic work with ${frequency.minutes} minute intervals")
    }
    
    /**
     * Cancels all scheduled notification checks
     */
    fun cancelNotificationChecks(context: Context) {
        WorkManager.getInstance(context).cancelUniqueWork(GitNotificationWorker.WORK_NAME)
        Log.d(TAG, "Cancelled background notification checks")
    }
    
    /**
     * Gets the current status of background notification work
     */
    suspend fun getWorkStatus(context: Context): WorkInfo.State? {
        val workInfos = WorkManager.getInstance(context)
            .getWorkInfosForUniqueWork(GitNotificationWorker.WORK_NAME)
            .await()
        
        return workInfos.firstOrNull()?.state
    }
    
    /**
     * Forces an immediate notification check (useful for testing)
     */
    fun triggerImmediateCheck(context: Context) {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()
        
        val workRequest = OneTimeWorkRequestBuilder<GitNotificationWorker>()
            .setConstraints(constraints)
            .addTag("${GitNotificationWorker.TAG}_immediate")
            .build()
        
        WorkManager.getInstance(context).enqueue(workRequest)
        Log.d(TAG, "Triggered immediate notification check")
    }
    
    /**
     * Initializes background notification scheduling based on current settings
     * Should be called when the app starts
     */
    suspend fun initialize(context: Context) {
        Log.d(TAG, "Initializing background notification scheduler")
        scheduleNotificationChecks(context)
    }
}
