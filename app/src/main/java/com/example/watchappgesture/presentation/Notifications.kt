package com.example.watchappgesture.presentation

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.wear.compose.foundation.lazy.ScalingLazyColumn
import androidx.wear.compose.foundation.lazy.rememberScalingLazyListState
import androidx.wear.compose.material3.Button
import androidx.wear.compose.material3.ButtonDefaults
import androidx.wear.compose.material3.CompactButton
import androidx.wear.compose.material3.MaterialTheme
import androidx.wear.compose.material3.ScrollIndicator
import androidx.wear.compose.material3.Text
import com.example.watchappgesture.presentation.models.Notification
import com.example.watchappgesture.presentation.providers.GitProviderType
import com.example.watchappgesture.presentation.providers.ProviderFactory
import com.example.watchappgesture.presentation.getSelectedProvider
import com.example.watchappgesture.presentation.repository.GitRepository
import com.example.watchappgesture.presentation.models.toAppError
import com.example.watchappgesture.presentation.components.LoadingIndicator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

@Composable
fun NotificationScreen(
    token: String, 
    context: android.content.Context,
    dialogViewModel: com.example.watchappgesture.presentation.viewmodels.DialogViewModel
) {

    var notifications: List<Notification> by remember { mutableStateOf(emptyList<Notification>()) }
    var isLoading by remember { mutableStateOf(true) }
    val notificationListState = rememberScalingLazyListState()
    val repository: GitRepository = remember { GitRepository(context) }

    LaunchedEffect(Unit) {
        repository.getNotifications(token)
            .onSuccess { notificationList ->
                notifications = notificationList
                isLoading = false
                println("Got notifications: ${notifications.size}")
            }
            .onFailure { error ->
                isLoading = false
                val appError = error.toAppError()
                println("Error fetching notifications: ${appError.message}")
                dialogViewModel.showDialog(
                    title = "Error Loading Notifications",
                    description = appError.message ?: "Failed to load notifications."
                )
            }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black),
        contentAlignment = Alignment.Center
    ) {
        if (isLoading) {
            LoadingIndicator(message = "Loading notifications...")
        } else {
            ScalingLazyColumn(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                state = notificationListState,
                userScrollEnabled = true
            ) {
                item {
                    Text(
                        text = "Notifications",
                        textAlign = TextAlign.Center,
                        fontWeight = FontWeight.Bold,
                        fontSize = 15.sp,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 12.dp, bottom = 4.dp)
                    )
                }
                // Say how many unread
                item {
                    Text(
                        text = "${notifications.size} Unread",
                        modifier = Modifier.fillMaxWidth(),
                        textAlign = TextAlign.Center,
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                }
                items(notifications.size) { index ->
                    val notification = notifications[index]

                    var showButtons by remember { mutableStateOf(false) }
                    Card(
                        colors = CardDefaults.elevatedCardColors(
                            containerColor = Color(0xFF1E1E1E),
                            contentColor = Color.White
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 2.dp, vertical = 4.dp)
                            .clickable {
                                showButtons = !showButtons
                            }
                    ) {
                        Column(modifier = Modifier.padding(10.dp)) {
                            Text(
                                text = notification.type,
                                color = Color.Gray,
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Bold,
                            )
                            Text(
                                text = notification.title,
                                color = MaterialTheme.colorScheme.primary,
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Bold
                            )
                            Text(
                                text = notification.repositoryName,
                                color = Color.White,
                                fontWeight = FontWeight.SemiBold,
                                fontSize = 12.sp
                            )
                            Text(
                                text = "Updated: " + notification.updatedAt.replace("T", " ")
                                    .replace("Z", ""),
                                color = Color.Gray,
                                fontSize = 10.sp
                            )
                            if (showButtons) {
                                // Mark as read button
                                var markAsReadRequested by remember { mutableStateOf(false) }
                                if (markAsReadRequested) {
                                    LaunchedEffect(notification.id) {
                                        repository.markNotificationAsRead(token, notification.id)
                                            .onSuccess {
                                                // Reload notifications after marking as read
                                                repository.getNotifications(token)
                                                    .onSuccess { updatedNotifications ->
                                                        notifications = updatedNotifications
                                                    }
                                                    .onFailure { error ->
                                                        val appError = error.toAppError()
                                                        dialogViewModel.showDialog(
                                                            title = "Error",
                                                            description = "Failed to reload notifications: ${appError.message}"
                                                        )
                                                    }
                                            }
                                            .onFailure { error ->
                                                val appError = error.toAppError()
                                                println("Error marking notification as read: ${appError.message}")
                                                dialogViewModel.showDialog(
                                                    title = "Error",
                                                    description = "Failed to mark notification as read: ${appError.message}"
                                                )
                                            }
                                        markAsReadRequested = false
                                    }
                                }
                                CompactButton(
                                    enabled = !markAsReadRequested,
                                    onClick = {
                                        println("Marking notification as read: ${notification.id}")
                                        markAsReadRequested = true
                                    },
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = Color.Transparent,
                                        contentColor = MaterialTheme.colorScheme.onPrimary
                                    ),
                                    border = ButtonDefaults.outlinedButtonBorder(enabled = true),
                                    modifier = Modifier.padding(top = 4.dp)
                                ) {
                                    Text("Mark as Read", color = Color.White)
                                }
                            }
                        }
                    }
                }
            }
            ScrollIndicator(
                state = notificationListState,
                modifier = Modifier
                    .fillMaxHeight()
                    .align(Alignment.CenterEnd)
                    .padding(horizontal = 4.dp)
            )
        }
    }
}