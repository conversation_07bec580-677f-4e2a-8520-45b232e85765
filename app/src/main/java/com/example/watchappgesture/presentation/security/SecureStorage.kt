package com.example.watchappgesture.presentation.security

import android.content.Context
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.example.watchappgesture.presentation.dataStore
import com.example.watchappgesture.presentation.utils.Constants
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import java.io.IOException
import java.security.MessageDigest
import java.util.Base64
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.SecretKeySpec

/**
 * Secure storage utility for sensitive data like access tokens
 * Note: This is a basic implementation. For production, consider using Android Keystore
 */
object SecureStorage {
    
    private const val ALGORITHM = "AES"
    private const val TRANSFORMATION = "AES/ECB/PKCS5Padding"
    
    /**
     * Generate a simple key from device-specific information
     * In production, use Android Keystore for better security
     */
    private fun generateKey(context: Context): SecretKey {
        val deviceId = android.provider.Settings.Secure.getString(
            context.contentResolver,
            android.provider.Settings.Secure.ANDROID_ID
        )
        val keyBytes = MessageDigest.getInstance("SHA-256")
            .digest(deviceId.toByteArray())
            .sliceArray(0..15) // AES-128 requires 16 bytes
        return SecretKeySpec(keyBytes, ALGORITHM)
    }
    
    /**
     * Encrypt a string value
     */
    private fun encrypt(context: Context, plainText: String): String {
        val cipher = Cipher.getInstance(TRANSFORMATION)
        cipher.init(Cipher.ENCRYPT_MODE, generateKey(context))
        val encryptedBytes = cipher.doFinal(plainText.toByteArray())
        return Base64.getEncoder().encodeToString(encryptedBytes)
    }
    
    /**
     * Decrypt a string value
     */
    private fun decrypt(context: Context, encryptedText: String): String {
        val cipher = Cipher.getInstance(TRANSFORMATION)
        cipher.init(Cipher.DECRYPT_MODE, generateKey(context))
        val encryptedBytes = Base64.getDecoder().decode(encryptedText)
        val decryptedBytes = cipher.doFinal(encryptedBytes)
        return String(decryptedBytes)
    }
    
    /**
     * Save encrypted access token
     */
    suspend fun saveAccessToken(context: Context, token: String) {
        val encryptedToken = encrypt(context, token)
        val ACCESS_TOKEN_KEY = stringPreferencesKey(Constants.ACCESS_TOKEN_KEY)
        withContext(Dispatchers.IO) {
            context.dataStore.edit { preferences ->
                preferences[ACCESS_TOKEN_KEY] = encryptedToken
            }
        }
    }
    
    /**
     * Retrieve and decrypt access token
     */
    suspend fun getAccessToken(context: Context): String? {
        val ACCESS_TOKEN_KEY = stringPreferencesKey(Constants.ACCESS_TOKEN_KEY)
        return try {
            val encryptedToken = context.dataStore.data
                .catch { exception ->
                    if (exception is IOException) emit(androidx.datastore.preferences.core.emptyPreferences()) else throw exception
                }
                .map { preferences -> preferences[ACCESS_TOKEN_KEY] }
                .first()
            
            encryptedToken?.let { decrypt(context, it) }
        } catch (e: Exception) {
            // If decryption fails, return null (token might be corrupted)
            null
        }
    }
    
    /**
     * Clear stored access token
     */
    suspend fun clearAccessToken(context: Context) {
        val ACCESS_TOKEN_KEY = stringPreferencesKey(Constants.ACCESS_TOKEN_KEY)
        withContext(Dispatchers.IO) {
            context.dataStore.edit { preferences ->
                preferences.remove(ACCESS_TOKEN_KEY)
            }
        }
    }
}