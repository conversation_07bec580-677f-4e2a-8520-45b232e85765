package com.example.watchappgesture.presentation.workers

import android.content.Context
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.example.watchappgesture.presentation.getUserAccessToken
import com.example.watchappgesture.presentation.notifications.GitNotificationManager
import com.example.watchappgesture.presentation.notifications.NotificationChannelManager
import com.example.watchappgesture.presentation.notifications.NotificationStateManager
import com.example.watchappgesture.presentation.repository.GitRepository

/**
 * WorkManager worker that checks for new Git notifications in the background
 */
class GitNotificationWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {
    
    companion object {
        const val TAG = "GitNotificationWorker"
        const val WORK_NAME = "git_notification_check"
    }
    
    override suspend fun doWork(): Result {
        Log.d(TAG, "Starting background notification check")
        
        return try {
            // Check if notifications are enabled at system level
            if (!NotificationChannelManager.areNotificationsEnabled(applicationContext)) {
                Log.d(TAG, "System notifications are disabled, skipping check")
                return Result.success()
            }
            
            // Get access token
            val token = getUserAccessToken(applicationContext)
            if (token.isNullOrEmpty()) {
                Log.d(TAG, "No access token available, skipping notification check")
                return Result.success()
            }
            
            // Fetch current notifications
            val repository = GitRepository(applicationContext)
            val notificationsResult = repository.getNotifications(token)
            
            notificationsResult.fold(
                onSuccess = { notifications ->
                    Log.d(TAG, "Fetched ${notifications.size} total notifications")
                    
                    // Find new notifications compared to last check
                    val newNotifications = NotificationStateManager.findNewNotifications(
                        applicationContext,
                        notifications
                    )
                    
                    Log.d(TAG, "Found ${newNotifications.size} new notifications")
                    
                    // Show system notification if there are new notifications
                    if (newNotifications.isNotEmpty()) {
                        GitNotificationManager.showNewNotificationsAlert(
                            applicationContext,
                            newNotifications
                        )
                        Log.d(TAG, "Displayed system notification for ${newNotifications.size} new notifications")
                    }
                    
                    Result.success()
                },
                onFailure = { error ->
                    Log.e(TAG, "Failed to fetch notifications: ${error.message}", error)
                    // Don't fail the work - just log and continue
                    // This prevents WorkManager from retrying unnecessarily
                    Result.success()
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "Unexpected error in notification worker", e)
            // Return success to prevent unnecessary retries
            Result.success()
        }
    }
}
