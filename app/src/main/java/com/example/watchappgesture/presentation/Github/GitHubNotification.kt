package com.example.watchappgesture.presentation.Github

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class GitHubNotification(
    @<PERSON><PERSON>(name = "id") val id: String,
    @<PERSON><PERSON>(name = "unread") val unread: <PERSON><PERSON><PERSON>,
    @<PERSON><PERSON>(name = "reason") val reason: String,
    @Json(name = "updated_at") val updatedAt: String,
    val subject: Subject,
    @Json(name = "repository") val repository: Repository,
    @<PERSON>son(name = "url") val url: String,
    @<PERSON><PERSON>(name = "subscription_url") val subscriptionUrl: String,
)

@JsonClass(generateAdapter = true)
data class Subject(
    @J<PERSON>(name = "title") val title: String,
    @J<PERSON>(name = "type") val type: String,
)

@JsonClass(generateAdapter = true)
data class Repository(
    @<PERSON><PERSON>(name = "full_name") val fullName: String
)