package com.example.watchappgesture.presentation.providers

enum class GitProviderType { 
    GITHUB, 
    GITLAB 
}

object ProviderFactory {
    fun createProvider(type: GitProviderType): GitProvider {
        return when (type) {
            GitProviderType.GITHUB -> GitHubProvider()
            GitProviderType.GITLAB -> GitLabProvider()
        }
    }
    
    fun getAvailableProviders(): List<GitProviderType> {
        return listOf(GitProviderType.GITHUB, GitProviderType.GITLAB)
    }
}