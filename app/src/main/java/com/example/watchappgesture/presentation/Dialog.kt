package com.example.watchappgesture.presentation

import android.R
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.wear.compose.material3.AlertDialog
import androidx.wear.compose.material3.AlertDialogDefaults
import androidx.wear.compose.material3.Text
import com.example.watchappgesture.presentation.viewmodels.DialogViewModel



@Composable
fun MessageDialog(
    dialogViewModel: DialogViewModel,
    themeColor: Color = Color(0xFF00FA9A)
) {
    val dialogState by dialogViewModel.dialogState.collectAsState()
    
    AlertDialog(
        visible = dialogState.isVisible,
        onDismissRequest = dialogState.onDismiss,
        title = {
            Text(text = dialogState.title)
        },
        text = {
            Text(text = dialogState.description)
        },
        confirmButton = { },
        dismissButton = {
            AlertDialogDefaults.DismissButton(
                colors = androidx.wear.compose.material3.IconButtonDefaults.iconButtonColors(
                    contentColor = Color.Black,
                    containerColor = themeColor
                ),
                onClick = dialogState.onDismiss,
                content = {
                    Icon(
                        painter = androidx.compose.ui.res.painterResource(id = R.drawable.ic_menu_close_clear_cancel),
                        contentDescription = "Dismiss",
                        tint = Color.Black,
                        modifier = Modifier.size(24.dp)
                    )
                }
            )
        }
    )
}

@Composable
fun MessageDialog(
    visible: Boolean,
    onDismiss: () -> Unit,
    title: String = "Title",
    description: String = "No description provided.",
    themeColor: Color = Color(0xFF00FA9A)
) {
    AlertDialog(
        visible = visible,
        onDismissRequest = onDismiss,
        title = {
            Text(text = title)
        },
        text = {
            Text(text = description)
        },
        confirmButton = { },
        dismissButton = {
            AlertDialogDefaults.DismissButton(
                colors = androidx.wear.compose.material3.IconButtonDefaults.iconButtonColors(
                    contentColor = Color.Black,
                    containerColor = themeColor
                ),
                onClick = onDismiss,
                content = {
                    Icon(
                        painter = androidx.compose.ui.res.painterResource(id = R.drawable.ic_menu_close_clear_cancel),
                        contentDescription = "Dismiss",
                        tint = Color.Black,
                        modifier = Modifier.size(24.dp)
                    )
                }
            )
        }
    )
}
