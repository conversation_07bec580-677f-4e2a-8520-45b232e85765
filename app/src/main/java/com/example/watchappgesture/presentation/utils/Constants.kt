package com.example.watchappgesture.presentation.utils

/**
 * Application-wide constants
 */
object Constants {
    // Theme colors
    const val DEFAULT_THEME_COLOR = "#00FA9A"
    
    // UI dimensions
    const val AVATAR_SIZE_DP = 50
    const val COMPACT_AVATAR_SIZE_DP = 24
    const val ICON_SIZE_DP = 25
    const val PROGRESS_INDICATOR_SIZE_DP = 50
    const val PROGRESS_STROKE_WIDTH_DP = 6
    
    // DataStore keys
    const val ACCESS_TOKEN_KEY = "access_token"
    const val THEME_COLOR_KEY = "theme_color"
    const val PROVIDER_KEY = "selected_provider"
    
    // Default values
    const val DEFAULT_PROVIDER = "GITHUB"
    const val DATASTORE_NAME = "settings"
    
    // Network
    const val DEVICE_CODE_POLL_INTERVAL_SECONDS = 5
    
    // UI text
    const val APP_NAME = "Git Watch"
    const val LOADING_TEXT = "Loading..."
    const val ERROR_TITLE = "Error"
    const val SUCCESS_TITLE = "Success"
}