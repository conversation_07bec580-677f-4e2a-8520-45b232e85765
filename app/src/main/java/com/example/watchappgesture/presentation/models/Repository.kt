package com.example.watchappgesture.presentation.models

data class Repository(
    val id: String,
    val name: String,
    val fullName: String,
    val isPrivate: Boolean,
    val description: String?,
    val language: String?,
    val stars: Int,
    val forks: Int,
    val watchers: Int,
    val openIssues: Int,
    val url: String,
    val createdAt: String,
    val updatedAt: String,
    val visibility: String,
    val owner: RepositoryOwner
)

data class RepositoryOwner(
    val username: String,
    val id: String,
    val avatarUrl: String,
    val profileUrl: String
)