package com.example.watchappgesture.presentation.Github

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class GitHubRepoEvent(
    @<PERSON><PERSON>(name = "id") val id: String,
    @<PERSON><PERSON>(name = "type") val type: String,
    @<PERSON><PERSON>(name = "actor") val actor: Actor,
    @<PERSON><PERSON>(name = "payload") val payload: Payload?,
    @<PERSON><PERSON>(name = "public") val public: <PERSON><PERSON><PERSON>,
    @<PERSON><PERSON>(name = "created_at") val createdAt: String,
)


data class Payload(
    val repository_id: Long?,
    val push_id: Long?,
    val size: Int?, // Make nullable
    val distinct_size: Int?,
    val ref: String?,
    val head: String?,
    val before: String?,
    val commits: List<Commit>?,
    val pull_request: PullRequest?,
)


@JsonClass(generateAdapter = true)
data class Actor(
    @Json(name = "id") val id: Long,
    @<PERSON><PERSON>(name = "login") val login: String,
    @<PERSON><PERSON>(name = "display_login") val displayLogin: String,
    @<PERSON><PERSON>(name = "gravatar_id") val gravatarId: String,
    @<PERSON><PERSON>(name = "url") val url: String,
    @Json(name = "avatar_url") val avatarUrl: String
)

data class PullRequest(
    val id: Long,
    val number: Int,
    val state: String,
    val title: String,
    val user: Owner,
    val created_at: String,
    val updated_at: String,
    val closed_at: String?,
    val merged_at: String?,
    val merge_commit_sha: String?,
    val url: String,
    val head: Head?,
    val base: Head?,
)
data class Head(
    val ref: String
)

@JsonClass(generateAdapter = true)
data class Commit(
    @Json(name = "sha") val sha: String,
    @Json(name = "author") val author: Author,
    @Json(name = "message") val message: String,
    @Json(name = "distinct") val distinct: Boolean,
    @Json(name = "url") val url: String
)

@JsonClass(generateAdapter = true)
data class Author(
    @Json(name = "name") val name: String
)