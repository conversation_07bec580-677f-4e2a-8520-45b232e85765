package com.example.watchappgesture.presentation.Github

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

@JsonClass(generateAdapter = true)
data class GitHubRepository(
    @<PERSON><PERSON>(name = "id") val id: Long,
    @<PERSON><PERSON>(name = "name") val name: String,
    @Json(name = "full_name") val fullName: String,
    @Json(name = "private") val isPrivate: <PERSON><PERSON><PERSON>,
    @<PERSON><PERSON>(name = "html_url") val htmlUrl: String,
    @Json(name = "description") val description: String?,
    @<PERSON><PERSON>(name = "owner") val owner: Owner,
    @<PERSON><PERSON>(name = "created_at") val createdAt: String,
    @J<PERSON>(name = "updated_at") val updatedAt: String,
    @<PERSON><PERSON>(name = "language") val language: String?,
    @Json(name = "visibility") val visibility: String,
    @<PERSON><PERSON>(name = "watchers_count") val watchersCount: Int,
    @<PERSON><PERSON>(name = "open_issues") val openIssues: Int,
    @Json(name = "stargazers_count") val stargazersCount: Int,
    @<PERSON><PERSON>(name = "forks_count") val forksCount: Int,
)

@JsonClass(generateAdapter = true)
data class Owner(
    @Json(name = "login") val login: String,
    @Json(name = "id") val id: Long,
    @Json(name = "avatar_url") val avatarUrl: String,
    @Json(name = "html_url") val htmlUrl: String
)