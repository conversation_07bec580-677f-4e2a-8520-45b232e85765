package com.example.watchappgesture.presentation

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ProgressIndicatorDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.wear.compose.foundation.lazy.ScalingLazyColumn
import androidx.wear.compose.foundation.lazy.items
import androidx.wear.compose.foundation.lazy.rememberScalingLazyListState
import androidx.wear.compose.material.Chip
import androidx.wear.compose.material.ChipDefaults
import androidx.wear.compose.material3.ScrollIndicator
import coil.compose.AsyncImage
import com.example.watchappgesture.R
import com.example.watchappgesture.presentation.models.RepoEvent
import com.example.watchappgesture.presentation.models.Repository
import com.example.watchappgesture.presentation.providers.ProviderFactory
import com.example.watchappgesture.presentation.getSelectedProvider
import com.example.watchappgesture.presentation.repository.GitRepository
import com.example.watchappgesture.presentation.models.toAppError
import com.example.watchappgesture.presentation.components.LoadingIndicator

@Composable
fun Repos(
    context: Context,
    themeColor: Color,
    token: String,
    goBack: () -> Unit,
    dialogViewModel: com.example.watchappgesture.presentation.viewmodels.DialogViewModel,
    onRepoClick: (String) -> Unit
) {
    val userRepos = remember { mutableStateOf<List<Repository>>(emptyList()) }
    val repository: GitRepository = remember { GitRepository(context) }

    LaunchedEffect(Unit) {
        if (userRepos.value.isNotEmpty()) return@LaunchedEffect // Avoid refetching if already loaded
        
        repository.getRepositories(token)
            .onSuccess { repositories ->
                userRepos.value = repositories
            }
            .onFailure { error ->
                val appError = error.toAppError()
                println("Error fetching Repos: ${appError.message}")
                dialogViewModel.showDialog(
                    title = "Error Loading Repositories",
                    description = appError.message ?: "Failed to load your repositories.",
                    onDismiss = { goBack() }
                )
            }
    }


    ScalingLazyColumn(
        modifier = Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (userRepos.value.isEmpty()) {
            item {
                LoadingIndicator(message = "Loading repositories...")
            }
        } else {
            item {
                Spacer(modifier = Modifier.size(30.dp))
            }
            item {
                Text(
                    text = "My Repos",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            }
            // Repos are already sorted in the repository layer
            items(userRepos.value) { repo ->
                Chip(
                    onClick = { onRepoClick(repo.fullName) },
                    modifier = Modifier.fillMaxWidth(1f),
                    label = {
                        Text(
                            text = repo.name, color = themeColor, fontWeight = FontWeight.Bold
                        )
                    },
                    secondaryLabel = {
                        Text(
                            text = repo.owner.username, color = Color.LightGray, maxLines = 1
                        )
                    },
                    icon = {
                        AsyncImage(
                            model = repo.owner.avatarUrl,
                            contentDescription = "User Avatar",
                            modifier = Modifier
                                .clip(CircleShape)
                                .size(24.dp)
                        )
                    },
                    colors = ChipDefaults.chipColors(backgroundColor = Color.Black),
                    border = ChipDefaults.outlinedChipBorder(
                        borderColor = Color.DarkGray, borderWidth = 1.dp
                    )
                )
            }
//            item {
//                Text(
//                    text = "Only public repos are being shown since extra " + "permissions are required to access private repos.",
//                    color = Color.LightGray,
//                    style = MaterialTheme.typography.bodySmall,
//                )
//            }
        }
    }
    ScrollIndicator(
        state = rememberScalingLazyListState(),
    )
}


@Composable
fun RepoDetailsScreen(
    repoFullName: String,
    context: Context,
    token: String,
    onBack: () -> Boolean,
    themeColor: androidx.compose.ui.graphics.Color,
    dialogViewModel: com.example.watchappgesture.presentation.viewmodels.DialogViewModel
) {
    val repoDetails = remember { mutableStateOf<Repository?>(null) }
    val recentEvents = remember { mutableStateOf<List<RepoEvent>>(emptyList()) }
    val repository: GitRepository = remember { GitRepository(context) }

    LaunchedEffect(repoFullName) {
        // Fetch repository details
        repository.getRepositoryByName(token, repoFullName)
            .onSuccess { repo ->
                if (repo != null) {
                    repoDetails.value = repo
                    
                    // Fetch recent events for the repo
                    repository.getRepoEvents(token, repoFullName)
                        .onSuccess { events ->
                            recentEvents.value = events
                        }
                        .onFailure { error ->
                            val appError = error.toAppError()
                            println("Error fetching repo events: ${appError.message}")
                            // Don't show error for events, just log it
                        }
                } else {
                    dialogViewModel.showDialog(
                        title = "Repository Not Found",
                        description = "The requested repository could not be found.",
                        onDismiss = { onBack() }
                    )
                }
            }
            .onFailure { error ->
                val appError = error.toAppError()
                println("Error fetching Repo Details: ${appError.message}")
                dialogViewModel.showDialog(
                    title = "Error Loading Repository",
                    description = appError.message ?: "Failed to load repository details.",
                    onDismiss = { onBack() }
                )
            }
    }

    if (repoDetails.value != null) {
        ScalingLazyColumn(
            modifier = Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally
        ) {
            item {
                Spacer(modifier = Modifier.size(30.dp))
            }
            item {
                Text(
                    text = repoDetails.value!!.name,
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            }
            item {
                AsyncImage(
                    model = repoDetails.value!!.owner.avatarUrl,
                    contentDescription = "User Avatar",
                    modifier = Modifier
                        .clip(CircleShape)
                        .size(48.dp)
                )
            }
            item {
                Text(
                    text = "Owner: ${repoDetails.value!!.owner.username}",
                    color = Color.White,
                )
            }
            item {
                Text(
                    text = "\"${repoDetails.value!!.description ?: "No description"}\"",
                    color = Color.LightGray,
                    fontSize = MaterialTheme.typography.bodyMedium.fontSize,
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )
            }
            item {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_star),
                        contentDescription = "Stars",
                        tint = Color(0xFFFFAA00),
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.size(6.dp))
                    Text(
                        text = "Stars: ${repoDetails.value!!.stars}",
                        color = Color(0xFFFFAA00),
                    )
                }
            }
            item {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_fork),
                        contentDescription = "Forks",
                        tint = Color(0xFF0099FF),
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.size(6.dp))
                    Text(
                        text = "Forks: ${repoDetails.value!!.forks}",
                        color = Color(0xFF0099FF),
                    )
                }
            }

            item {
                // Events title
                Text(
                    text = "Recent Events",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )
            }

            items(recentEvents.value) { event ->
                Card(
                    colors = CardDefaults.elevatedCardColors(
                        containerColor = Color(0xFF1E1E1E), contentColor = Color.White
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 2.dp, vertical = 4.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(10.dp)
                    ) {
                        AsyncImage(
                            model = event.actorAvatarUrl,
                            contentDescription = "Actor Avatar",
                            modifier = Modifier
                                .size(28.dp)
                                .clip(CircleShape)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = event.type,
                                color = themeColor,
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Bold
                            )
                            Text(
                                text = "by ${event.actorName}",
                                fontSize = 10.sp,
                                color = Color.Gray
                            )
                            Text(
                                text = event.createdAt.replace("T", " ").replace("Z", ""),
                                fontSize = 9.sp,
                                color = Color.Gray
                            )
                        }
                    }
                }
            }
        }
    } else {
        // Show loading or error state
    }


}