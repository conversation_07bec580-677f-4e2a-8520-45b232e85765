package com.example.watchappgesture.presentation.Github

import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.wear.compose.foundation.lazy.ScalingLazyColumn
import androidx.wear.compose.foundation.lazy.rememberScalingLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.wear.compose.material3.Button
import androidx.wear.compose.material3.ButtonDefaults
import androidx.wear.compose.material3.MaterialTheme
import androidx.wear.compose.material3.Text
import com.example.watchappgesture.presentation.viewmodels.DialogViewModel
import com.example.watchappgesture.presentation.checkInternet
import com.example.watchappgesture.presentation.saveUserAccessToken
import com.example.watchappgesture.presentation.getSelectedProvider
import com.example.watchappgesture.presentation.saveSelectedProvider
import com.example.watchappgesture.presentation.providers.GitProviderType
import com.example.watchappgesture.presentation.providers.ProviderFactory
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


@Composable
fun UserCodePreview(userCode: String, verificationUri: String, context: Context) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black.copy(alpha = 0.8f))
            .wrapContentSize(Alignment.Center)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(24.dp)
        ) {
            Text(
                text = userCode,
                color = Color.White,
                fontSize = 20.sp,
                fontWeight = FontWeight.Black,
            )
            Text(
                text = verificationUri.replace("https://", ""),
                color = MaterialTheme.colorScheme.primary,
                fontSize = 12.sp,
                fontWeight = FontWeight.SemiBold,
            )
        }
    }
}


@Composable
fun SetUp(context: Context, onSignedIn: (String) -> Unit, themeColor: Color, dialogViewModel: DialogViewModel) {
    var userCode by remember { mutableStateOf("") }
    var deviceCode by remember { mutableStateOf("") }
    var verificationUri by remember { mutableStateOf("") }
    var isLoadingCode by remember { mutableStateOf(false) }
    var selectedProvider by remember { mutableStateOf<GitProviderType>(GitProviderType.GITHUB) }

    LaunchedEffect(Unit) {
        selectedProvider = getSelectedProvider(context)
    }

    Box(
        modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center
    ) {
        if (userCode.isNotEmpty()) {
            UserCodePreview(userCode, verificationUri, context)
        } else {
            val listState = rememberScalingLazyListState()
            
            ScalingLazyColumn(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                state = listState,
                userScrollEnabled = true
            ) {
                item {
                    // Provider Selection
                    Text(
                        text = "Choose Provider",
                        color = Color.White,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.SemiBold,
                        modifier = Modifier.padding(bottom = 12.dp)
                    )
                }
                
                item {
                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        modifier = Modifier.padding(horizontal = 16.dp)
                    ) {
                        ProviderFactory.getAvailableProviders().forEach { provider ->
                            val isSelected = selectedProvider == provider
                            Button(
                                onClick = {
                                    CoroutineScope(Dispatchers.IO).launch {
                                        saveSelectedProvider(context, provider)
                                        selectedProvider = provider
                                    }
                                },
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = if (isSelected) themeColor else Color.Gray
                                ),
                                modifier = Modifier.weight(1f)
                            ) {
                                Text(
                                    text = provider.name,
                                    fontSize = 10.sp,
                                    color = if (isSelected) Color.Black else Color.White
                                )
                            }
                        }
                    }
                }
                
                item {
                    Spacer(modifier = Modifier.height(20.dp))
                }
                
                item {
                    Button(
                enabled = !isLoadingCode,
                // Use the theme color
                modifier = Modifier
                    .padding(16.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF1f1f1f),
                ),
                onClick = {
                    CoroutineScope(Dispatchers.IO).launch {
                        val hasInternet = checkInternet(context)

                        if (!hasInternet) {
                            withContext(Dispatchers.Main) {
                                dialogViewModel.showDialog(
                                    title = "Unable to Connect",
                                    description = "Please connect to the internet and try again."
                                )
                            }
                            return@launch
                        }

                        try {
                            isLoadingCode = true
                            val provider = ProviderFactory.createProvider(selectedProvider)
                            
                            // Request device code from selected provider
                            val deviceCodeResponse = provider.requestDeviceCode()
                            userCode = deviceCodeResponse.userCode
                            deviceCode = deviceCodeResponse.deviceCode
                            verificationUri = deviceCodeResponse.verificationUri

                            println("Got device code response: userCode=$userCode, deviceCode=$deviceCode")
                            isLoadingCode = false

                            // Keep polling the provider to see if the user has authorized the app
                            while (true) {
                                val accessToken = provider.pollForAccessToken(deviceCode)
                                if (accessToken != null) {
                                    println("Got the access token! $accessToken")
                                    saveUserAccessToken(context, accessToken)
                                    withContext(Dispatchers.Main) {
                                        onSignedIn(accessToken)
                                    }
                                    break
                                }
                                delay((deviceCodeResponse.interval * 1000).toLong()) // Use provider-specific interval
                            }
                        } catch (e: Exception) {
                            withContext(Dispatchers.Main) {
                                isLoadingCode = false
                                dialogViewModel.showDialog(
                                    title = "Authentication Error",
                                    description = "Failed to authenticate with ${selectedProvider.name}: ${e.message}"
                                )
                            }
                        }
                    }
                }) {
                        Text(
                            text = "Sign in with ${selectedProvider.name}",
                            color = Color.White
                        )
                    }
                }
            }
        }
    }
}
