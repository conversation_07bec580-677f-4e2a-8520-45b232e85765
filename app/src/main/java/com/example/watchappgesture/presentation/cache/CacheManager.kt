package com.example.watchappgesture.presentation.cache

import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.concurrent.ConcurrentHashMap

/**
 * Simple in-memory cache manager for API responses
 */
class CacheManager<K, V> {
    private val cache = ConcurrentHashMap<K, CacheEntry<V>>()
    private val mutex = Mutex()
    
    data class CacheEntry<V>(
        val value: V,
        val timestamp: Long,
        val ttlMs: Long = DEFAULT_TTL_MS
    ) {
        fun isExpired(): Boolean = System.currentTimeMillis() - timestamp > ttlMs
    }
    
    suspend fun get(key: K): V? = mutex.withLock {
        val entry = cache[key]
        if (entry?.isExpired() == true) {
            cache.remove(key)
            null
        } else {
            entry?.value
        }
    }
    
    suspend fun put(key: K, value: V, ttlMs: Long = DEFAULT_TTL_MS) = mutex.withLock {
        cache[key] = CacheEntry(value, System.currentTimeMillis(), ttlMs)
    }
    
    suspend fun remove(key: K) = mutex.withLock {
        cache.remove(key)
    }
    
    suspend fun clear() = mutex.withLock {
        cache.clear()
    }
    
    companion object {
        private const val DEFAULT_TTL_MS = 5 * 60 * 1000L // 5 minutes
    }
}

/**
 * Global cache instances for different data types
 */
object AppCache {
    val userCache = CacheManager<String, com.example.watchappgesture.presentation.models.User>()
    val repositoriesCache = CacheManager<String, List<com.example.watchappgesture.presentation.models.Repository>>()
    val notificationsCache = CacheManager<String, List<com.example.watchappgesture.presentation.models.Notification>>()
}