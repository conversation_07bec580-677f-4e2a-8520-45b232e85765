package com.example.watchappgesture.presentation.notifications

import android.content.Context
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringSetPreferencesKey
import com.example.watchappgesture.presentation.dataStore
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map

/**
 * Manages notification state persistence to detect new notifications
 */
object NotificationStateManager {
    
    private val LAST_NOTIFICATION_COUNT_KEY = intPreferencesKey("last_notification_count")
    private val LAST_NOTIFICATION_IDS_KEY = stringSetPreferencesKey("last_notification_ids")
    
    /**
     * Data class to hold notification state
     */
    data class NotificationState(
        val count: Int,
        val notificationIds: Set<String>
    )
    
    /**
     * Gets the last stored notification state
     */
    suspend fun getLastNotificationState(context: Context): NotificationState {
        val preferences = context.dataStore.data.first()
        val count = preferences[LAST_NOTIFICATION_COUNT_KEY] ?: 0
        val ids = preferences[LAST_NOTIFICATION_IDS_KEY] ?: emptySet()
        return NotificationState(count, ids)
    }
    
    /**
     * Saves the current notification state
     */
    suspend fun saveNotificationState(context: Context, state: NotificationState) {
        context.dataStore.edit { preferences ->
            preferences[LAST_NOTIFICATION_COUNT_KEY] = state.count
            preferences[LAST_NOTIFICATION_IDS_KEY] = state.notificationIds
        }
    }
    
    /**
     * Compares current notifications with last stored state to find new ones
     */
    suspend fun findNewNotifications(
        context: Context,
        currentNotifications: List<com.example.watchappgesture.presentation.models.Notification>
    ): List<com.example.watchappgesture.presentation.models.Notification> {
        val lastState = getLastNotificationState(context)
        val currentUnreadNotifications = currentNotifications.filter { it.unread }
        val currentIds = currentUnreadNotifications.map { it.id }.toSet()
        
        // Find notifications that are new (not in last stored IDs)
        val newNotifications = currentUnreadNotifications.filter { notification ->
            notification.id !in lastState.notificationIds
        }
        
        // Update stored state with current notifications
        val newState = NotificationState(
            count = currentUnreadNotifications.size,
            notificationIds = currentIds
        )
        saveNotificationState(context, newState)
        
        return newNotifications
    }
    
    /**
     * Clears stored notification state (useful for testing or reset)
     */
    suspend fun clearNotificationState(context: Context) {
        context.dataStore.edit { preferences ->
            preferences.remove(LAST_NOTIFICATION_COUNT_KEY)
            preferences.remove(LAST_NOTIFICATION_IDS_KEY)
        }
    }
}
