package com.example.watchappgesture.presentation.providers

import com.example.watchappgesture.presentation.models.User
import com.example.watchappgesture.presentation.models.Repository
import com.example.watchappgesture.presentation.models.RepositoryOwner
import com.example.watchappgesture.presentation.models.Notification
import com.example.watchappgesture.presentation.models.RepoEvent
import com.example.watchappgesture.presentation.Github.GitHubUser
import com.example.watchappgesture.presentation.Github.GitHubRepository
import com.example.watchappgesture.presentation.Github.GitHubNotification
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.MediaType.Companion.toMediaTypeOrNull

class GitHubProvider : GitProvider {
    override val providerName = "GitHub"
    override val authUrl = "https://github.com/login/device"
    override val clientId = "Ov23liVxTJf2IFPnmfnb"
    override val scopes = "read:user,notifications,repo"
    
    private val client = OkHttpClient()
    private val moshi = Moshi.Builder()
        .add(KotlinJsonAdapterFactory())
        .build()

    override suspend fun getUserInfo(token: String): User {
        val request = Request.Builder()
            .url("https://api.github.com/user")
            .header("Authorization", "Bearer $token")
            .build()

        return withContext(Dispatchers.IO) {
            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) throw Exception("Unexpected code $response")

                val body = response.body?.string()
                    ?: throw Exception("Empty body")

                val adapter = moshi.adapter(GitHubUser::class.java)
                val githubUser = adapter.fromJson(body) ?: throw Exception("Failed to parse JSON")
                
                // Convert GitHub-specific model to generic model
                User(
                    id = githubUser.id.toString(),
                    username = githubUser.login,
                    displayName = githubUser.name,
                    avatarUrl = githubUser.avatarUrl,
                    profileUrl = githubUser.html_url,
                    publicRepos = githubUser.publicRepos,
                    followers = githubUser.followers,
                    following = githubUser.following,
                    bio = githubUser.bio,
                    joinDate = githubUser.created_at
                )
            }
        }
    }

    override suspend fun getRepositories(token: String): List<Repository> {
        val request = Request.Builder()
            .url("https://api.github.com/user/repos")
            .header("Authorization", "Bearer $token")
            .build()

        return withContext(Dispatchers.IO) {
            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) throw Exception("Unexpected code $response")

                val body = response.body?.string()
                    ?: throw Exception("Empty body")

                val adapter = moshi.adapter<List<GitHubRepository>>(
                    Types.newParameterizedType(List::class.java, GitHubRepository::class.java)
                )
                val githubRepos = adapter.fromJson(body) ?: throw Exception("Failed to parse JSON")
                
                // Convert GitHub-specific models to generic models
                githubRepos.map { githubRepo ->
                    Repository(
                        id = githubRepo.id.toString(),
                        name = githubRepo.name,
                        fullName = githubRepo.fullName,
                        isPrivate = githubRepo.isPrivate,
                        description = githubRepo.description,
                        language = githubRepo.language,
                        stars = githubRepo.stargazersCount,
                        forks = githubRepo.forksCount,
                        watchers = githubRepo.watchersCount,
                        openIssues = githubRepo.openIssues,
                        url = githubRepo.htmlUrl,
                        createdAt = githubRepo.createdAt,
                        updatedAt = githubRepo.updatedAt,
                        visibility = githubRepo.visibility,
                        owner = RepositoryOwner(
                            username = githubRepo.owner.login,
                            id = githubRepo.owner.id.toString(),
                            avatarUrl = githubRepo.owner.avatarUrl,
                            profileUrl = githubRepo.owner.htmlUrl
                        )
                    )
                }
            }
        }
    }

    override suspend fun getNotifications(token: String): List<Notification> {
        val request = Request.Builder()
            .url("https://api.github.com/notifications")
            .header("Authorization", "Bearer $token")
            .build()

        return withContext(Dispatchers.IO) {
            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) throw Exception("Unexpected code $response")

                val body = response.body?.string()
                    ?: throw Exception("Empty body")

                val adapter = moshi.adapter<List<GitHubNotification>>(
                    Types.newParameterizedType(List::class.java, GitHubNotification::class.java)
                )
                val githubNotifications = adapter.fromJson(body) ?: throw Exception("Failed to parse JSON")
                
                // Convert GitHub-specific models to generic models
                githubNotifications.map { githubNotification ->
                    Notification(
                        id = githubNotification.id,
                        title = githubNotification.subject.title,
                        type = githubNotification.subject.type,
                        repositoryName = githubNotification.repository.fullName,
                        reason = githubNotification.reason,
                        updatedAt = githubNotification.updatedAt,
                        url = githubNotification.url,
                        unread = githubNotification.unread
                    )
                }
            }
        }
    }

    override suspend fun markNotificationAsRead(token: String, notificationId: String) {
        val request = Request.Builder()
            .url("https://api.github.com/notifications/threads/$notificationId")
            .header("Authorization", "Bearer $token")
            .patch("".toRequestBody(null))
            .build()

        withContext(Dispatchers.IO) {
            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) throw Exception("Unexpected code $response")
            }
        }
    }

    override suspend fun getRepoEvents(token: String, repoFullName: String): List<RepoEvent> {
        val request = Request.Builder()
            .url("https://api.github.com/repos/$repoFullName/events")
            .header("Authorization", "Bearer $token")
            .build()

        return withContext(Dispatchers.IO) {
            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) throw Exception("Unexpected code $response")

                val body = response.body?.string() ?: throw Exception("Empty body")

                // Parse as a generic JSON array to extract basic event info
                val adapter = moshi.adapter<List<Map<String, Any>>>(
                    Types.newParameterizedType(List::class.java, 
                        Types.newParameterizedType(Map::class.java, String::class.java, Any::class.java)
                    )
                )
                val events = adapter.fromJson(body) ?: throw Exception("Failed to parse JSON")

                // Convert to generic RepoEvent models
                events.mapNotNull { eventMap ->
                    try {
                        val actor = eventMap["actor"] as? Map<String, Any>
                        RepoEvent(
                            id = eventMap["id"] as? String ?: "",
                            type = eventMap["type"] as? String ?: "",
                            actorName = actor?.get("login") as? String ?: "Unknown",
                            actorAvatarUrl = actor?.get("avatar_url") as? String ?: "",
                            createdAt = eventMap["created_at"] as? String ?: "",
                            isPublic = eventMap["public"] as? Boolean ?: true
                        )
                    } catch (e: Exception) {
                        null // Skip malformed events
                    }
                }
            }
        }
    }

    override suspend fun requestDeviceCode(): DeviceCodeResponse {
        val getCodeURL = "https://github.com/login/device/code?client_id=$clientId&scope=$scopes"
        val postBody = ""

        val request = Request.Builder()
            .url(getCodeURL)
            .post(postBody.toRequestBody(null))
            .build()

        return withContext(Dispatchers.IO) {
            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) throw Exception("Unexpected code $response")
                
                val bodyString = response.body?.string() ?: throw Exception("Empty response body")
                val userCode = bodyString.substringAfter("user_code=").substringBefore("&")
                val deviceCode = bodyString.substringAfter("device_code=").substringBefore("&")
                
                if (userCode.isEmpty() || deviceCode.isEmpty()) {
                    throw Exception("Failed to parse device code response")
                }
                
                DeviceCodeResponse(
                    userCode = userCode,
                    deviceCode = deviceCode,
                    verificationUri = authUrl,
                    interval = 5
                )
            }
        }
    }

    override suspend fun pollForAccessToken(deviceCode: String): String? {
        val grantType = "urn:ietf:params:oauth:grant-type:device_code"
        val pollUrl = "https://github.com/login/oauth/access_token?client_id=$clientId&device_code=$deviceCode&grant_type=$grantType"
        
        val request = Request.Builder()
            .url(pollUrl)
            .post("".toRequestBody("application/x-www-form-urlencoded".toMediaTypeOrNull()))
            .build()

        return withContext(Dispatchers.IO) {
            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    return@withContext null
                }
                
                val responseBody = response.body?.string() ?: return@withContext null
                
                // Check for errors (authorization_pending is expected during polling)
                if (responseBody.contains("error")) {
                    if (responseBody.contains("authorization_pending")) {
                        return@withContext null // User hasn't authorized yet
                    } else {
                        throw Exception("OAuth error: $responseBody")
                    }
                }
                
                // Extract access token
                if (responseBody.contains("access_token")) {
                    val accessToken = responseBody.substringAfter("access_token=").substringBefore("&")
                    if (accessToken.isNotEmpty()) {
                        return@withContext accessToken
                    }
                }
                
                return@withContext null
            }
        }
    }
}