/* While this template provides a good starting point for using Wear Compose, you can always
 * take a look at https://github.com/android/wear-os-samples/tree/main/ComposeStarter to find the
 * most up to date changes to the libraries and their usages.
 */

package com.example.watchappgesture.presentation

//noinspection SuspiciousImport
import android.Manifest
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.annotation.RequiresPermission
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ProgressIndicatorDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.emptyPreferences
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import androidx.wear.compose.foundation.AnchorType
import androidx.wear.compose.foundation.CurvedDirection
import androidx.wear.compose.foundation.CurvedLayout
import androidx.wear.compose.foundation.CurvedModifier
import androidx.wear.compose.foundation.background
import androidx.wear.compose.foundation.curvedRow
import androidx.wear.compose.foundation.sizeIn
import androidx.wear.compose.material3.MaterialTheme
import androidx.wear.compose.material3.curvedText
import com.example.watchappgesture.presentation.Github.SetUp
import com.example.watchappgesture.presentation.providers.GitProviderType
import com.example.watchappgesture.presentation.providers.ProviderFactory
import com.example.watchappgesture.presentation.models.User
import com.example.watchappgesture.presentation.models.AppError
import com.example.watchappgesture.presentation.models.toAppError
import com.example.watchappgesture.presentation.repository.GitRepository
import com.example.watchappgesture.presentation.viewmodels.DialogViewModel
import com.example.watchappgesture.presentation.utils.Constants
import com.example.watchappgesture.presentation.components.LoadingIndicator
import com.example.watchappgesture.presentation.security.SecureStorage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import java.io.IOException

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        installSplashScreen()
        super.onCreate(savedInstanceState)
        setContent {
            App()
        }
    }
}

val Context.dataStore by preferencesDataStore(name = Constants.DATASTORE_NAME)

suspend fun saveUserAccessToken(context: Context, token: String) {
    if (token.isEmpty()) {
        SecureStorage.clearAccessToken(context)
    } else {
        SecureStorage.saveAccessToken(context, token)
    }
}

suspend fun getUserAccessToken(context: Context): String? {
    return SecureStorage.getAccessToken(context)
}

@RequiresPermission(Manifest.permission.ACCESS_NETWORK_STATE)
fun checkInternet(context: Context): Boolean {
    val connectivityManager =
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    val activeNetwork: Network? = connectivityManager.activeNetwork ?: return false
    val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)
        ?: return false
    return networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
}

suspend fun getUserThemeColor(context: Context): String {
    val THEME_COLOR_KEY = stringPreferencesKey(Constants.THEME_COLOR_KEY)
    return context.dataStore.data
        .catch { exception ->
            if (exception is IOException) emit(emptyPreferences()) else throw exception
        }
        .map { preferences -> preferences[THEME_COLOR_KEY] ?: Constants.DEFAULT_THEME_COLOR }
        .first()
}

suspend fun saveSelectedProvider(context: Context, provider: GitProviderType) {
    val PROVIDER_KEY = stringPreferencesKey(Constants.PROVIDER_KEY)
    withContext(Dispatchers.IO) {
        context.dataStore.edit { preferences ->
            preferences[PROVIDER_KEY] = provider.name
        }
    }
}

suspend fun getSelectedProvider(context: Context): GitProviderType {
    val PROVIDER_KEY = stringPreferencesKey(Constants.PROVIDER_KEY)
    val providerName = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) emit(emptyPreferences()) else throw exception
        }
        .map { preferences -> preferences[PROVIDER_KEY] ?: Constants.DEFAULT_PROVIDER }
        .first()
    return GitProviderType.valueOf(providerName)
}

@Composable
fun App() {
    val context = LocalContext.current
    val dialogViewModel: DialogViewModel = viewModel()
    var accessToken by remember { mutableStateOf<String?>(null) }
    var themeColor by remember { mutableStateOf(Color(android.graphics.Color.parseColor(Constants.DEFAULT_THEME_COLOR))) }

    // Load theme and token on startup
    LaunchedEffect(Unit) {
        accessToken = getUserAccessToken(context)
        val hex = getUserThemeColor(context)
        themeColor = Color(android.graphics.Color.parseColor(hex))
    }

    // Dialog shown globally
    MessageDialog(
        dialogViewModel = dialogViewModel,
        themeColor = themeColor,
    )

    MaterialTheme(
        colorScheme = MaterialTheme.colorScheme.copy(
            primary = themeColor,
            onPrimary = Color.Black,
            onSurface = Color.White,
        ),
    ) {
        when {
            accessToken.isNullOrEmpty() -> {
                SetUp(context = context, onSignedIn = { token ->
                    accessToken = token
                }, themeColor = themeColor, dialogViewModel = dialogViewModel)
            }

            else -> {
                MainScreen(accessToken!!, context, themeColor, dialogViewModel)
            }
        }

        val versionName = context.packageManager
            .getPackageInfo(context.packageName, 0).versionName

        CurvedLayout(
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = 4.dp),
            anchor = 90f, // Anchor at the bottom
            anchorType = AnchorType.Center,
        ) {
            curvedRow(
                modifier =
                    CurvedModifier
                        .background(color = Color(0xFFFFA407), StrokeCap.Round),
            ) {
                curvedText(
                    text = "Preview: V$versionName",
                    color = Color.DarkGray,
                    modifier =
                        CurvedModifier
                            .sizeIn(0f, 80f),
                    fontSize = 8.sp,
                    // Flip the text to be readable on a round watch face
                    angularDirection = CurvedDirection.Angular.CounterClockwise
                )
            }
        }
    }
}

fun restartApp(context: Context) {
    val packageManager = context.packageManager
    val intent = packageManager.getLaunchIntentForPackage(context.packageName)
    intent?.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK)
    context.startActivity(intent)
    (context as? ComponentActivity)?.finish()
}

@Composable
fun MainScreen(token: String, context: Context, themeColor: Color, dialogViewModel: DialogViewModel) {
    var userInfo by remember { mutableStateOf<User?>(null) }
    val repository: GitRepository = remember { GitRepository(context) }

    LaunchedEffect(token) {
        if (!checkInternet(context)) {
            dialogViewModel.showDialog(
                title = "Cannot load account",
                description = "Please connect to the internet and try again."
            )
            return@LaunchedEffect
        }
        
        repository.getUserInfo(token)
            .onSuccess { user ->
                userInfo = user
            }
            .onFailure { error ->
                val appError = error.toAppError()
                
                when (appError) {
                    is AppError.TokenExpiredError, is AppError.AuthError -> {
                        saveUserAccessToken(context, "")
                        dialogViewModel.showDialog(
                            title = "Authentication Error",
                            description = "Your session has expired. Please sign in again.",
                            onDismiss = { restartApp(context) }
                        )
                    }
                    else -> {
                        dialogViewModel.showDialog(
                            title = "Error Loading Account",
                            description = appError.message ?: "An unknown error occurred."
                        )
                    }
                }
            }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(10.dp),
        contentAlignment = Alignment.TopCenter
    ) {
        if (userInfo == null) {
            LoadingIndicator(message = "Loading account...")
        } else {
            HomeScreen(
                userInfo,
                token,
                context,
                themeColor,
                dialogViewModel
            )
        }
    }
}