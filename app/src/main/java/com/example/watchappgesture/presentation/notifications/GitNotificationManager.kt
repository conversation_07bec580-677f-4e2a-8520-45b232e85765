package com.example.watchappgesture.presentation.notifications

import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import androidx.core.app.NotificationCompat
import com.example.watchappgesture.R
import com.example.watchappgesture.presentation.MainActivity
import com.example.watchappgesture.presentation.models.Notification

/**
 * Utility class for managing Android system notifications for Git notifications
 */
object GitNotificationManager {
    
    private const val NOTIFICATION_ID = 1001
    private const val MAX_LINES = 5 // Maximum lines to show in expanded notification
    
    /**
     * Shows a system notification for new Git notifications
     */
    fun showNewNotificationsAlert(
        context: Context,
        newNotifications: List<Notification>
    ) {
        if (newNotifications.isEmpty()) return
        
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        
        // Create intent to open the app when notification is tapped
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("open_notifications", true) // Signal to open notifications screen
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = if (newNotifications.size == 1) {
            // Single notification
            createSingleNotification(context, newNotifications.first(), pendingIntent)
        } else {
            // Multiple notifications
            createMultipleNotificationsAlert(context, newNotifications, pendingIntent)
        }
        
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
    
    /**
     * Creates a notification for a single new Git notification
     */
    private fun createSingleNotification(
        context: Context,
        gitNotification: Notification,
        pendingIntent: PendingIntent
    ): android.app.Notification {
        return NotificationCompat.Builder(context, NotificationChannelManager.GIT_NOTIFICATIONS_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle("New Git Notification")
            .setContentText(gitNotification.title)
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("${gitNotification.title}\n\nRepository: ${gitNotification.repositoryName}\nReason: ${gitNotification.reason}")
            )
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setCategory(NotificationCompat.CATEGORY_SOCIAL)
            .build()
    }
    
    /**
     * Creates a notification for multiple new Git notifications
     */
    private fun createMultipleNotificationsAlert(
        context: Context,
        newNotifications: List<Notification>,
        pendingIntent: PendingIntent
    ): android.app.Notification {
        val count = newNotifications.size
        val inboxStyle = NotificationCompat.InboxStyle()
            .setBigContentTitle("$count New Git Notifications")
        
        // Add up to MAX_LINES notification titles
        newNotifications.take(MAX_LINES).forEach { notification ->
            inboxStyle.addLine("${notification.repositoryName}: ${notification.title}")
        }
        
        // Add summary if there are more notifications
        if (newNotifications.size > MAX_LINES) {
            inboxStyle.addLine("... and ${newNotifications.size - MAX_LINES} more")
        }
        
        return NotificationCompat.Builder(context, NotificationChannelManager.GIT_NOTIFICATIONS_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle("$count New Git Notifications")
            .setContentText("You have new activity across ${getUniqueRepositoryCount(newNotifications)} repositories")
            .setStyle(inboxStyle)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setCategory(NotificationCompat.CATEGORY_SOCIAL)
            .setNumber(count)
            .build()
    }
    
    /**
     * Gets the count of unique repositories in the notifications
     */
    private fun getUniqueRepositoryCount(notifications: List<Notification>): Int {
        return notifications.map { it.repositoryName }.toSet().size
    }
    
    /**
     * Cancels any existing Git notifications
     */
    fun cancelNotifications(context: Context) {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.cancel(NOTIFICATION_ID)
    }
}
