package com.example.watchappgesture.presentation.repository

import android.content.Context
import com.example.watchappgesture.presentation.models.AppError
import com.example.watchappgesture.presentation.models.Notification
import com.example.watchappgesture.presentation.models.RepoEvent
import com.example.watchappgesture.presentation.models.Repository
import com.example.watchappgesture.presentation.models.User
import com.example.watchappgesture.presentation.models.toAppError
import com.example.watchappgesture.presentation.providers.GitProvider
import com.example.watchappgesture.presentation.providers.ProviderFactory
import com.example.watchappgesture.presentation.getSelectedProvider
import com.example.watchappgesture.presentation.cache.AppCache
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Repository class that handles data operations and provides a clean API
 * for the UI layer while abstracting the underlying data sources
 */
class GitRepository(private val context: Context) {
    
    private suspend fun getProvider(): GitProvider {
        val selectedProvider = getSelectedProvider(context)
        return ProviderFactory.createProvider(selectedProvider)
    }
    
    /**
     * Fetches user information with proper error handling and caching
     */
    suspend fun getUserInfo(token: String): Result<User> = withContext(Dispatchers.IO) {
        runCatching {
            // Check cache first
            AppCache.userCache.get(token)?.let { cachedUser ->
                return@runCatching cachedUser
            }
            
            // Fetch from API if not cached
            val provider = getProvider()
            val user = provider.getUserInfo(token)
            
            // Cache the result
            AppCache.userCache.put(token, user)
            user
        }.recoverCatching { exception ->
            throw exception.toAppError()
        }
    }
    
    /**
     * Fetches user repositories with caching support
     */
    suspend fun getRepositories(token: String): Result<List<Repository>> = withContext(Dispatchers.IO) {
        runCatching {
            // Check cache first
            AppCache.repositoriesCache.get(token)?.let { cachedRepos ->
                return@runCatching cachedRepos
            }
            
            // Fetch from API if not cached
            val provider = getProvider()
            val repositories = provider.getRepositories(token).sortedByDescending { it.updatedAt }
            
            // Cache the result
            AppCache.repositoriesCache.put(token, repositories)
            repositories
        }.recoverCatching { exception ->
            throw exception.toAppError()
        }
    }
    
    /**
     * Fetches notifications with proper error handling and caching
     */
    suspend fun getNotifications(token: String): Result<List<Notification>> = withContext(Dispatchers.IO) {
        runCatching {
            // Check cache first (shorter TTL for notifications)
            AppCache.notificationsCache.get(token)?.let { cachedNotifications ->
                return@runCatching cachedNotifications
            }
            
            // Fetch from API if not cached
            val provider = getProvider()
            val notifications = provider.getNotifications(token)
            
            // Cache the result with shorter TTL (2 minutes for notifications)
            AppCache.notificationsCache.put(token, notifications, 2 * 60 * 1000L)
            notifications
        }.recoverCatching { exception ->
            throw exception.toAppError()
        }
    }
    
    /**
     * Marks a notification as read and invalidates cache
     */
    suspend fun markNotificationAsRead(token: String, notificationId: String): Result<Unit> = withContext(Dispatchers.IO) {
        runCatching {
            val provider = getProvider()
            provider.markNotificationAsRead(token, notificationId)
            
            // Invalidate notifications cache since data changed
            AppCache.notificationsCache.remove(token)
            Unit
        }.recoverCatching { exception ->
            throw exception.toAppError()
        }
    }
    
    /**
     * Fetches repository events
     */
    suspend fun getRepoEvents(token: String, repoFullName: String): Result<List<RepoEvent>> = withContext(Dispatchers.IO) {
        runCatching {
            val provider = getProvider()
            provider.getRepoEvents(token, repoFullName)
        }.recoverCatching { exception ->
            throw exception.toAppError()
        }
    }
    
    /**
     * Finds a specific repository by full name
     */
    suspend fun getRepositoryByName(token: String, repoFullName: String): Result<Repository?> = withContext(Dispatchers.IO) {
        runCatching {
            val repositories = getRepositories(token).getOrThrow()
            repositories.find { it.fullName == repoFullName }
        }.recoverCatching { exception ->
            throw exception.toAppError()
        }
    }
}