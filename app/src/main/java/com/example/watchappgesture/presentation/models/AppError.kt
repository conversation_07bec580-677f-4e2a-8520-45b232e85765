package com.example.watchappgesture.presentation.models

/**
 * Sealed class representing different types of application errors
 */
sealed class AppError : Exception() {
    object NetworkError : AppError() {
        override val message: String = "Network connection error. Please check your internet connection."
    }
    
    object AuthError : AppError() {
        override val message: String = "Authentication failed. Please sign in again."
    }
    
    data class ApiError(val code: Int, override val message: String) : AppError()
    
    data class UnknownError(override val message: String) : AppError()
    
    object TokenExpiredError : AppError() {
        override val message: String = "Your session has expired. Please sign in again."
    }
    
    object RateLimitError : AppError() {
        override val message: String = "Rate limit exceeded. Please try again later."
    }
}

/**
 * Extension function to convert generic throwables to AppError
 */
fun Throwable.toAppError(): AppError {
    return when {
        message?.contains("network", ignoreCase = true) == true -> AppError.NetworkError
        message?.contains("unauthorized", ignoreCase = true) == true -> AppError.AuthError
        message?.contains("401") == true -> AppError.TokenExpiredError
        message?.contains("403") == true -> AppError.RateLimitError
        else -> AppError.UnknownError(message ?: "An unknown error occurred")
    }
}