package com.example.watchappgesture.presentation.providers

import com.example.watchappgesture.presentation.models.User
import com.example.watchappgesture.presentation.models.Repository
import com.example.watchappgesture.presentation.models.RepositoryOwner
import com.example.watchappgesture.presentation.models.Notification
import com.example.watchappgesture.presentation.models.RepoEvent
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import java.net.URLEncoder

// GitLab-specific data models
@JsonClass(generateAdapter = true)
data class GitLabUser(
    val id: Long,
    val username: String,
    val name: String?,
    @Json(name = "avatar_url") val avatarUrl: String?,
    val bio: String?,
    @<PERSON><PERSON>(name = "public_repos") val publicRepos: Int? = 0,
    val followers: Int? = 0,
    val following: Int? = 0,
    @Json(name = "web_url") val webUrl: String? = null,
    @Json(name = "created_at") val createdAt: String? = null
)

@JsonClass(generateAdapter = true)
data class GitLabProject(
    val id: Long,
    val name: String,
    @Json(name = "name_with_namespace") val nameWithNamespace: String,
    @Json(name = "path_with_namespace") val pathWithNamespace: String,
    val visibility: String,
    val description: String?,
    @Json(name = "web_url") val webUrl: String,
    @Json(name = "created_at") val createdAt: String,
    @Json(name = "last_activity_at") val lastActivityAt: String,
    @Json(name = "default_branch") val defaultBranch: String?,
    @Json(name = "star_count") val starCount: Int,
    @Json(name = "forks_count") val forksCount: Int,
    @Json(name = "open_issues_count") val openIssuesCount: Int,
    val owner: GitLabOwner?
)

@JsonClass(generateAdapter = true)
data class GitLabOwner(
    val id: Long,
    val username: String,
    val name: String,
    @Json(name = "avatar_url") val avatarUrl: String,
    @Json(name = "web_url") val webUrl: String
)

// GitLab Todo (Notification) models
@JsonClass(generateAdapter = true)
data class GitLabTodo(
    val id: Long,
    val project: GitLabTodoProject?,
    val author: GitLabTodoAuthor,
    @Json(name = "action_name") val actionName: String,
    @Json(name = "target_type") val targetType: String,
    val target: GitLabTodoTarget?,
    @Json(name = "target_url") val targetUrl: String,
    val body: String?,
    val state: String,
    @Json(name = "created_at") val createdAt: String,
    @Json(name = "updated_at") val updatedAt: String
)

@JsonClass(generateAdapter = true)
data class GitLabTodoProject(
    val id: Long,
    val name: String,
    @Json(name = "name_with_namespace") val nameWithNamespace: String,
    val path: String,
    @Json(name = "path_with_namespace") val pathWithNamespace: String
)

@JsonClass(generateAdapter = true)
data class GitLabTodoAuthor(
    val id: Long,
    val username: String,
    val name: String,
    @Json(name = "avatar_url") val avatarUrl: String?,
    @Json(name = "web_url") val webUrl: String
)

@JsonClass(generateAdapter = true)
data class GitLabTodoTarget(
    val id: Long?,
    val iid: Long?,
    val title: String?,
    val state: String?
)

// GitLab Event models
@JsonClass(generateAdapter = true)
data class GitLabEvent(
    val id: Long,
    @Json(name = "project_id") val projectId: Long,
    @Json(name = "action_name") val actionName: String,
    @Json(name = "target_id") val targetId: Long?,
    @Json(name = "target_type") val targetType: String?,
    @Json(name = "author_id") val authorId: Long,
    @Json(name = "target_title") val targetTitle: String?,
    @Json(name = "created_at") val createdAt: String,
    val author: GitLabEventAuthor,
    @Json(name = "author_username") val authorUsername: String
)

@JsonClass(generateAdapter = true)
data class GitLabEventAuthor(
    val id: Long,
    val username: String,
    val name: String,
    @Json(name = "avatar_url") val avatarUrl: String?,
    @Json(name = "web_url") val webUrl: String
)

class GitLabProvider : GitProvider {
    override val providerName = "GitLab"
    override val authUrl = "https://gitlab.com/login/device"
    override val clientId = "4f88a1fe2b8db7b080757f83800911324995b33846b4ab55bbff804ad962d593"
    override val scopes = "api"
    
    private val client = OkHttpClient()
    private val moshi = Moshi.Builder()
        .add(KotlinJsonAdapterFactory())
        .build()

    override suspend fun getUserInfo(token: String): User {
        val request = Request.Builder()
            .url("https://gitlab.com/api/v4/user")
            .header("Authorization", "Bearer $token")
            .build()

        return withContext(Dispatchers.IO) {
            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) throw Exception("Unexpected code $response")

                val body = response.body?.string()
                    ?: throw Exception("Empty body")

                val adapter = moshi.adapter(GitLabUser::class.java)
                val gitlabUser = adapter.fromJson(body) ?: throw Exception("Failed to parse JSON")
                
                // Convert GitLab-specific model to generic model
                User(
                    id = gitlabUser.id.toString(),
                    username = gitlabUser.username,
                    displayName = gitlabUser.name,
                    avatarUrl = gitlabUser.avatarUrl,
                    profileUrl = gitlabUser.webUrl,
                    publicRepos = gitlabUser.publicRepos ?: 0,
                    followers = gitlabUser.followers ?: 0,
                    following = gitlabUser.following ?: 0,
                    bio = gitlabUser.bio,
                    joinDate = gitlabUser.createdAt
                )
            }
        }
    }

    override suspend fun getRepositories(token: String): List<Repository> {
        val request = Request.Builder()
            .url("https://gitlab.com/api/v4/projects?membership=true&simple=true")
            .header("Authorization", "Bearer $token")
            .build()

        return withContext(Dispatchers.IO) {
            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) throw Exception("Unexpected code $response")

                val body = response.body?.string()
                    ?: throw Exception("Empty body")

                val adapter = moshi.adapter<List<GitLabProject>>(
                    Types.newParameterizedType(List::class.java, GitLabProject::class.java)
                )
                val gitlabProjects = adapter.fromJson(body) ?: throw Exception("Failed to parse JSON")
                
                // Convert GitLab-specific models to generic models
                gitlabProjects.map { gitlabProject ->
                    Repository(
                        id = gitlabProject.id.toString(),
                        name = gitlabProject.name,
                        fullName = gitlabProject.pathWithNamespace,
                        isPrivate = gitlabProject.visibility == "private",
                        description = gitlabProject.description,
                        language = null, // GitLab doesn't provide primary language in this endpoint
                        stars = gitlabProject.starCount,
                        forks = gitlabProject.forksCount,
                        watchers = 0, // GitLab doesn't have watchers concept
                        openIssues = gitlabProject.openIssuesCount,
                        url = gitlabProject.webUrl,
                        createdAt = gitlabProject.createdAt,
                        updatedAt = gitlabProject.lastActivityAt,
                        visibility = gitlabProject.visibility,
                        owner = gitlabProject.owner?.let { owner ->
                            RepositoryOwner(
                                username = owner.username,
                                id = owner.id.toString(),
                                avatarUrl = owner.avatarUrl,
                                profileUrl = owner.webUrl
                            )
                        } ?: RepositoryOwner(
                            username = "unknown",
                            id = "0",
                            avatarUrl = "",
                            profileUrl = ""
                        )
                    )
                }
            }
        }
    }

    override suspend fun getNotifications(token: String): List<Notification> {
        val request = Request.Builder()
            .url("https://gitlab.com/api/v4/todos?state=pending")
            .header("Authorization", "Bearer $token")
            .build()

        return withContext(Dispatchers.IO) {
            try {
                client.newCall(request).execute().use { response ->
                    if (!response.isSuccessful) throw Exception("Unexpected code $response")

                    val body = response.body?.string()
                        ?: throw Exception("Empty body")

                    val adapter = moshi.adapter<List<GitLabTodo>>(
                        Types.newParameterizedType(List::class.java, GitLabTodo::class.java)
                    )
                    val gitlabTodos = adapter.fromJson(body) ?: throw Exception("Failed to parse JSON")

                    // Convert GitLab todos to generic notifications
                    gitlabTodos.map { todo ->
                        Notification(
                            id = todo.id.toString(),
                            title = todo.target?.title ?: todo.body ?: "Todo item",
                            type = todo.targetType,
                            repositoryName = todo.project?.pathWithNamespace ?: "Unknown",
                            reason = todo.actionName,
                            updatedAt = todo.updatedAt,
                            url = todo.targetUrl,
                            unread = todo.state == "pending"
                        )
                    }
                }
            } catch (e: Exception) {
                // Log error and return empty list to maintain compatibility
                emptyList()
            }
        }
    }

    override suspend fun markNotificationAsRead(token: String, notificationId: String) {
        val request = Request.Builder()
            .url("https://gitlab.com/api/v4/todos/$notificationId/mark_as_done")
            .header("Authorization", "Bearer $token")
            .post("".toRequestBody(null))
            .build()

        withContext(Dispatchers.IO) {
            try {
                client.newCall(request).execute().use { response ->
                    if (!response.isSuccessful) throw Exception("Unexpected code $response")
                }
            } catch (e: Exception) {
                // Re-throw to let the repository layer handle the error
                throw e
            }
        }
    }

    override suspend fun getRepoEvents(token: String, repoFullName: String): List<RepoEvent> {
        // URL-encode the project path for GitLab API
        val encodedProjectPath = URLEncoder.encode(repoFullName, "UTF-8")
        val request = Request.Builder()
            .url("https://gitlab.com/api/v4/projects/$encodedProjectPath/events")
            .header("Authorization", "Bearer $token")
            .build()

        return withContext(Dispatchers.IO) {
            try {
                client.newCall(request).execute().use { response ->
                    if (!response.isSuccessful) throw Exception("Unexpected code $response")

                    val body = response.body?.string()
                        ?: throw Exception("Empty body")

                    val adapter = moshi.adapter<List<GitLabEvent>>(
                        Types.newParameterizedType(List::class.java, GitLabEvent::class.java)
                    )
                    val gitlabEvents = adapter.fromJson(body) ?: throw Exception("Failed to parse JSON")

                    // Convert GitLab events to generic repo events
                    gitlabEvents.map { event ->
                        RepoEvent(
                            id = event.id.toString(),
                            type = event.actionName,
                            actorName = event.author.username,
                            actorAvatarUrl = event.author.avatarUrl ?: "",
                            createdAt = event.createdAt,
                            isPublic = true // GitLab events are visible if user has access to project
                        )
                    }
                }
            } catch (e: Exception) {
                // Log error and return empty list to maintain compatibility
                emptyList()
            }
        }
    }

    override suspend fun requestDeviceCode(): DeviceCodeResponse {
        // Try the minimal format from GitLab docs
        val requestBody = "client_id=$clientId&scope=${scopes.replace(",", "+")}"

        val request = Request.Builder()
            .url("https://gitlab.com/oauth/authorize_device")
            .post(requestBody.toRequestBody("application/x-www-form-urlencoded".toMediaTypeOrNull()))
            .header("Accept", "application/json")
            .build()

        return withContext(Dispatchers.IO) {
            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    val errorBody = response.body?.string()
                    throw Exception("GitLab device auth failed: ${response.code} ${response.message}. Body: $errorBody")
                }
                
                val body = response.body?.string() ?: throw Exception("Empty response body")
                
                // Try JSON first (what GitLab might actually return)
                try {
                    val adapter = moshi.adapter<Map<String, Any>>(Types.newParameterizedType(Map::class.java, String::class.java, Any::class.java))
                    val responseMap = adapter.fromJson(body)
                    if (responseMap != null) {
                        val userCode = responseMap["user_code"] as? String ?: throw Exception("Missing user_code")
                        val deviceCode = responseMap["device_code"] as? String ?: throw Exception("Missing device_code")
                        val verificationUri = responseMap["verification_uri"] as? String ?: authUrl
                        val interval = (responseMap["interval"] as? Double)?.toInt() ?: 5
                        
                        return@withContext DeviceCodeResponse(
                            userCode = userCode,
                            deviceCode = deviceCode,
                            verificationUri = verificationUri,
                            interval = interval
                        )
                    }
                } catch (e: Exception) {
                    // Fall back to form parsing
                }
                
                // Fallback: Try to parse as form-encoded response
                val userCode = body.substringAfter("user_code=").substringBefore("&")
                val deviceCode = body.substringAfter("device_code=").substringBefore("&")
                val verificationUri = if (body.contains("verification_uri=")) {
                    body.substringAfter("verification_uri=").substringBefore("&")
                } else {
                    authUrl
                }
                val interval = if (body.contains("interval=")) {
                    body.substringAfter("interval=").substringBefore("&").toIntOrNull() ?: 5
                } else {
                    5
                }
                
                if (userCode.isEmpty() || deviceCode.isEmpty()) {
                    throw Exception("Failed to parse device code response: $body")
                }
                
                DeviceCodeResponse(
                    userCode = userCode,
                    deviceCode = deviceCode,
                    verificationUri = verificationUri,
                    interval = interval
                )
            }
        }
    }

    override suspend fun pollForAccessToken(deviceCode: String): String? {
        val requestBody = "client_id=$clientId&device_code=$deviceCode&grant_type=urn:ietf:params:oauth:grant-type:device_code"

        val request = Request.Builder()
            .url("https://gitlab.com/oauth/token")
            .post(requestBody.toRequestBody("application/x-www-form-urlencoded".toMediaTypeOrNull()))
            .build()

        return withContext(Dispatchers.IO) {
            client.newCall(request).execute().use { response ->
                if (!response.isSuccessful) {
                    return@withContext null
                }
                
                val body = response.body?.string() ?: return@withContext null
                
                // Check for errors (authorization_pending is expected during polling)
                if (body.contains("error=")) {
                    val error = body.substringAfter("error=").substringBefore("&")
                    if (error == "authorization_pending") {
                        return@withContext null // User hasn't authorized yet
                    } else {
                        throw Exception("OAuth error: $error")
                    }
                }
                
                // Extract access token
                if (body.contains("access_token=")) {
                    val accessToken = body.substringAfter("access_token=").substringBefore("&")
                    if (accessToken.isNotEmpty()) {
                        return@withContext accessToken
                    }
                }
                
                return@withContext null
            }
        }
    }
}