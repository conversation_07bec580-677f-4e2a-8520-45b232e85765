package com.example.watchappgesture.presentation.providers

import com.example.watchappgesture.presentation.models.User
import com.example.watchappgesture.presentation.models.Repository
import com.example.watchappgesture.presentation.models.Notification
import com.example.watchappgesture.presentation.models.RepoEvent

data class DeviceCodeResponse(
    val userCode: String,
    val deviceCode: String,
    val verificationUri: String,
    val interval: Int = 5
)

interface GitProvider {
    suspend fun getUserInfo(token: String): User
    suspend fun getRepositories(token: String): List<Repository>
    suspend fun getNotifications(token: String): List<Notification>
    suspend fun markNotificationAsRead(token: String, notificationId: String)
    suspend fun getRepoEvents(token: String, repoFullName: String): List<RepoEvent>
    
    // OAuth Device Flow methods
    suspend fun requestDeviceCode(): DeviceCodeResponse
    suspend fun pollForAccessToken(deviceCode: String): String?
    
    val providerName: String
    val authUrl: String
    val clientId: String
    val scopes: String
}