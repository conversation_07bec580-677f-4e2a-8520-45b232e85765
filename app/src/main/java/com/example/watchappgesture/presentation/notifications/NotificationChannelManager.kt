package com.example.watchappgesture.presentation.notifications

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build

/**
 * Manages notification channels for Android 8+ compatibility
 */
object NotificationChannelManager {
    
    const val GIT_NOTIFICATIONS_CHANNEL_ID = "git_notifications"
    const val GIT_NOTIFICATIONS_CHANNEL_NAME = "Git Notifications"
    const val GIT_NOTIFICATIONS_CHANNEL_DESCRIPTION = "Notifications for new Git activity"
    
    /**
     * Creates notification channels required for the app
     */
    fun createNotificationChannels(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // Git notifications channel
            val gitNotificationsChannel = NotificationChannel(
                GIT_NOTIFICATIONS_CHANNEL_ID,
                GIT_NOTIFICATIONS_CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = GIT_NOTIFICATIONS_CHANNEL_DESCRIPTION
                enableLights(true)
                enableVibration(true)
                setShowBadge(true)
            }
            
            notificationManager.createNotificationChannel(gitNotificationsChannel)
        }
    }
    
    /**
     * Checks if notifications are enabled for the app
     */
    fun areNotificationsEnabled(context: Context): Boolean {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            notificationManager.areNotificationsEnabled() && 
            notificationManager.getNotificationChannel(GIT_NOTIFICATIONS_CHANNEL_ID)?.importance != NotificationManager.IMPORTANCE_NONE
        } else {
            notificationManager.areNotificationsEnabled()
        }
    }
}
