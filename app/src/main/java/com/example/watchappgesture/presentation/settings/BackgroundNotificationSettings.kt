package com.example.watchappgesture.presentation.settings

import android.content.Context
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import com.example.watchappgesture.presentation.dataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

/**
 * Manages settings for background notifications
 */
object BackgroundNotificationSettings {
    
    private val BACKGROUND_NOTIFICATIONS_ENABLED_KEY = booleanPreferencesKey("background_notifications_enabled")
    private val CHECK_FREQUENCY_MINUTES_KEY = intPreferencesKey("check_frequency_minutes")
    
    /**
     * Available check frequency options in minutes
     */
    enum class CheckFrequency(val minutes: Int, val displayName: String) {
        FIFTEEN_MINUTES(15, "15 minutes"),
        THIRTY_MINUTES(30, "30 minutes"),
        ONE_HOUR(60, "1 hour"),
        TWO_HOURS(120, "2 hours");
        
        companion object {
            fun fromMinutes(minutes: Int): CheckFrequency {
                return values().find { it.minutes == minutes } ?: FIFTEEN_MINUTES
            }
        }
    }
    
    /**
     * Gets whether background notifications are enabled
     */
    fun isBackgroundNotificationsEnabled(context: Context): Flow<Boolean> {
        return context.dataStore.data.map { preferences ->
            preferences[BACKGROUND_NOTIFICATIONS_ENABLED_KEY] ?: false
        }
    }
    
    /**
     * Sets whether background notifications are enabled
     */
    suspend fun setBackgroundNotificationsEnabled(context: Context, enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[BACKGROUND_NOTIFICATIONS_ENABLED_KEY] = enabled
        }
    }
    
    /**
     * Gets the current check frequency
     */
    fun getCheckFrequency(context: Context): Flow<CheckFrequency> {
        return context.dataStore.data.map { preferences ->
            val minutes = preferences[CHECK_FREQUENCY_MINUTES_KEY] ?: CheckFrequency.FIFTEEN_MINUTES.minutes
            CheckFrequency.fromMinutes(minutes)
        }
    }
    
    /**
     * Sets the check frequency
     */
    suspend fun setCheckFrequency(context: Context, frequency: CheckFrequency) {
        context.dataStore.edit { preferences ->
            preferences[CHECK_FREQUENCY_MINUTES_KEY] = frequency.minutes
        }
    }
    
    /**
     * Data class for all background notification settings
     */
    data class Settings(
        val enabled: Boolean,
        val frequency: CheckFrequency
    )
    
    /**
     * Gets all background notification settings as a flow
     */
    fun getSettings(context: Context): Flow<Settings> {
        return context.dataStore.data.map { preferences ->
            Settings(
                enabled = preferences[BACKGROUND_NOTIFICATIONS_ENABLED_KEY] ?: false,
                frequency = CheckFrequency.fromMinutes(
                    preferences[CHECK_FREQUENCY_MINUTES_KEY] ?: CheckFrequency.FIFTEEN_MINUTES.minutes
                )
            )
        }
    }
}
