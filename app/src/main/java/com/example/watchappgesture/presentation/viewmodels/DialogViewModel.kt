package com.example.watchappgesture.presentation.viewmodels

import androidx.lifecycle.ViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

data class DialogState(
    val isVisible: Boolean = false,
    val title: String = "",
    val description: String = "",
    val onDismiss: () -> Unit = {}
)

class DialogViewModel : ViewModel() {
    private val _dialogState = MutableStateFlow(DialogState())
    val dialogState: StateFlow<DialogState> = _dialogState.asStateFlow()
    
    fun showDialog(
        title: String,
        description: String,
        onDismiss: () -> Unit = { hideDialog() }
    ) {
        _dialogState.value = DialogState(
            isVisible = true,
            title = title,
            description = description,
            onDismiss = onDismiss
        )
    }
    
    fun hideDialog() {
        _dialogState.value = _dialogState.value.copy(isVisible = false)
    }
}