# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

StrapLab is a WearOS app built with Jetpack Compose and Kotlin that interfaces with Git provider APIs (GitHub, GitLab). Users can authenticate via OAuth, view notifications, manage repositories, and access account stats directly from their smartwatch.

## Development Commands

### Build & Run
```bash
# Build the app (debug)
./gradlew assembleDebug

# Build release APK
./gradlew assembleRelease

# Clean build
./gradlew clean

# Install to connected device
./gradlew installDebug
```

### Code Quality
```bash
# Run lint checks
./gradlew lint

# Run tests (if available)
./gradlew test
```

## Architecture

### Core Structure
- **MainActivity.kt**: Entry point, handles OAuth state, theme management, and global dialog state
- **HomeScreen.kt**: Main navigation hub with user stats and menu options using Wear Compose navigation
- **providers/**: Multi-provider architecture supporting GitHub and GitLab
  - **GitProvider.kt**: Common interface for all Git providers
  - **ProviderFactory.kt**: Factory pattern for creating provider instances
  - **GitHubProvider.kt / GitLabProvider.kt**: Provider-specific implementations
- **models/**: Unified data models (User, Repository, Notification) that abstract provider differences
- **Github/**: Legacy GitHub-specific code (being refactored to use provider system)

### Key Components
- **Navigation**: Uses Wear Compose SwipeDismissableNavHost with nested routes
- **Data Storage**: AndroidX DataStore for persisting access tokens, theme settings, and selected provider
- **HTTP Client**: OkHttp for API requests across all providers
- **JSON Parsing**: Moshi with Kotlin reflection for API response deserialization
- **Image Loading**: Coil for async image loading (avatars, QR codes)
- **QR Generation**: ZXing library for profile sharing functionality

### UI Architecture
- **Material 3 Design**: Uses latest Material 3 Expressive components for WearOS
- **Dynamic Theming**: User-customizable primary colors with persistent storage
- **Responsive Design**: ScalingLazyColumn for watch-optimized scrolling and navigation
- **Global State**: Centralized dialog state management via DialogState singleton
- **Context Awareness**: Network connectivity checks and error handling throughout

### Data Flow
1. Provider selection and OAuth tokens stored in DataStore preferences
2. ProviderFactory creates appropriate provider instance (GitHub/GitLab)
3. API calls made through provider interface with Bearer token authentication
4. Provider-specific JSON responses mapped to unified data models
5. UI state managed with Compose state and LaunchedEffect for async operations
6. Navigation between screens handled by Wear Compose navigation system

### Multi-Provider System
The app uses a provider pattern to support multiple Git services:
- **GitProviderType enum**: Defines available providers (GITHUB, GITLAB)
- **ProviderFactory**: Creates provider instances and lists available providers
- **Unified Models**: User, Repository, Notification models abstract provider differences
- **Provider Interface**: Common methods (getUserInfo, getRepositories, getNotifications, markNotificationAsRead)

### Package Structure
- `com.example.watchappgesture.presentation`: Main UI components and MainActivity
- `com.example.watchappgesture.presentation.providers`: Multi-provider system implementation
- `com.example.watchappgesture.presentation.models`: Unified data models
- `com.example.watchappgesture.presentation.Github`: Legacy GitHub-specific code
- `com.example.watchappgesture.presentation.theme`: Material 3 theming
- `com.example.watchappgesture.presentation.utils`: Utilities like QR code generation

### Build Configuration
- **Target SDK**: 36 (Android 14)
- **Min SDK**: 30 (WearOS requirement)
- **Kotlin**: 2.0.21 with compose compiler plugin
- **Gradle**: 8.11.1 with version catalog (libs.versions.toml)
- **ProGuard**: Disabled for debug builds
- **Key Dependencies**: WearOS Compose, Material 3, OkHttp, Moshi, Coil, DataStore, ZXing

### Development Notes
- The codebase is transitioning from GitHub-only to multi-provider architecture
- Legacy code in `Github/` directory should be refactored to use the provider system
- Network connectivity is checked before API calls with user-friendly error dialogs
- Authentication state is managed globally with automatic sign-out on API errors
- QR code generation uses dynamic theming colors for better visual integration