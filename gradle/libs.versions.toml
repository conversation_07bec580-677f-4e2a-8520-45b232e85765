[versions]
agp = "8.11.1"
composeMaterial3Version = "1.0.0-alpha05"
kotlin = "2.0.21"
playServicesWearable = "18.0.0"
composeBom = "2024.09.00"
composeMaterial = "1.2.1"
composeFoundation = "1.2.1"
wearToolingPreview = "1.0.0"
activityCompose = "1.8.0"
coreSplashscreen = "1.0.1"
material3Android = "1.3.2"
composeMaterial3 = "1.5.0-beta05"
roomCommonJvm = "2.7.2"
wearRemoteInteractions = "1.1.0"
composeNavigation = "1.4.1"

[libraries]
compose-material3-v100alpha05 = { module = "androidx.wear.compose:compose-material3", version.ref = "composeMaterial3Version" }
play-services-wearable = { group = "com.google.android.gms", name = "play-services-wearable", version.ref = "playServicesWearable" }
compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
ui = { group = "androidx.compose.ui", name = "ui" }
ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
compose-material = { group = "androidx.wear.compose", name = "compose-material", version.ref = "composeMaterial" }
compose-foundation = { group = "androidx.wear.compose", name = "compose-foundation", version.ref = "composeFoundation" }
wear-tooling-preview = { group = "androidx.wear", name = "wear-tooling-preview", version.ref = "wearToolingPreview" }
activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
core-splashscreen = { group = "androidx.core", name = "core-splashscreen", version.ref = "coreSplashscreen" }
material3-android = { group = "androidx.compose.material3", name = "material3-android", version.ref = "material3Android" }
compose-material3 = { group = "androidx.wear.compose", name = "compose-material3", version.ref = "composeMaterial3" }
room-common-jvm = { group = "androidx.room", name = "room-common-jvm", version.ref = "roomCommonJvm" }
wear-remote-interactions = { group = "androidx.wear", name = "wear-remote-interactions", version.ref = "wearRemoteInteractions" }
compose-navigation = { group = "androidx.wear.compose", name = "compose-navigation", version.ref = "composeNavigation" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }

